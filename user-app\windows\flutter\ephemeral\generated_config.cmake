# Generated code do not commit.
file(TO_CMAKE_PATH "E:\\program\\flutter_windows_3.32.7-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Documents\\augment-projects\\airplan\\user-app" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=E:\\program\\flutter_windows_3.32.7-stable\\flutter"
  "PROJECT_DIR=C:\\Users\\<USER>\\Documents\\augment-projects\\airplan\\user-app"
  "FLUTTER_ROOT=E:\\program\\flutter_windows_3.32.7-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=C:\\Users\\<USER>\\Documents\\augment-projects\\airplan\\user-app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=C:\\Users\\<USER>\\Documents\\augment-projects\\airplan\\user-app"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\Documents\\augment-projects\\airplan\\user-app\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNw==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZDdiNTIzYjM1Ng==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MzlkNmQ2ZTY5OQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=C:\\Users\\<USER>\\Documents\\augment-projects\\airplan\\user-app\\.dart_tool\\package_config.json"
)
